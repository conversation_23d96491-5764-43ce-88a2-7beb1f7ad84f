package com.ruoyi.common.keycloak.service;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.keycloak.KeycloakPrincipal;
import org.keycloak.KeycloakSecurityContext;
import org.keycloak.adapters.springsecurity.token.KeycloakAuthenticationToken;
import org.keycloak.representations.AccessToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Keycloak Token服务
 * 
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "keycloak.enabled", havingValue = "true", matchIfMissing = false)
public class KeycloakTokenService {

    private static final Logger log = LoggerFactory.getLogger(KeycloakTokenService.class);

    @Value("${keycloak.realm:ruoyi}")
    private String realm;

    /**
     * 验证Keycloak Token
     */
    public boolean validateKeycloakToken(String token) {
        try {
            // 这里可以添加更复杂的token验证逻辑
            return StringUtils.isNotEmpty(token) && token.startsWith("eyJ");
        } catch (Exception e) {
            log.error("Keycloak token validation failed", e);
            return false;
        }
    }

    /**
     * 从Keycloak Token中提取用户信息
     */
    public LoginUser getLoginUserFromKeycloakToken(String token) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication instanceof KeycloakAuthenticationToken) {
                KeycloakAuthenticationToken keycloakToken = (KeycloakAuthenticationToken) authentication;
                KeycloakPrincipal<?> principal = (KeycloakPrincipal<?>) keycloakToken.getPrincipal();
                KeycloakSecurityContext securityContext = principal.getKeycloakSecurityContext();
                AccessToken accessToken = securityContext.getToken();

                LoginUser loginUser = new LoginUser();
                // 设置用户基本信息
                loginUser.setUsername(accessToken.getPreferredUsername());
                loginUser.setUserid(Long.valueOf(accessToken.getSubject().hashCode()));
                
                // 设置权限信息
                Set<String> roles = accessToken.getRealmAccess().getRoles();
                loginUser.setPermissions(roles);

                return loginUser;
            }
        } catch (Exception e) {
            log.error("Failed to extract user info from Keycloak token", e);
        }
        return null;
    }

    /**
     * 创建Keycloak Token响应
     */
    public Map<String, Object> createKeycloakTokenResponse(String accessToken, String refreshToken, Long expiresIn) {
        Map<String, Object> response = new HashMap<>();
        response.put("access_token", accessToken);
        response.put("refresh_token", refreshToken);
        response.put("expires_in", expiresIn);
        response.put("token_type", "Bearer");
        return response;
    }

    /**
     * 从请求中获取Keycloak Token
     */
    public String getKeycloakToken(HttpServletRequest request) {
        String token = request.getHeader(SecurityConstants.AUTHORIZATION_HEADER);
        if (StringUtils.isNotEmpty(token) && token.startsWith(SecurityConstants.TOKEN_PREFIX)) {
            return token.substring(SecurityConstants.TOKEN_PREFIX.length());
        }
        return null;
    }

    /**
     * 检查是否为Keycloak Token
     */
    public boolean isKeycloakToken(String token) {
        // Keycloak JWT tokens typically start with "eyJ"
        return StringUtils.isNotEmpty(token) && token.startsWith("eyJ");
    }
}
