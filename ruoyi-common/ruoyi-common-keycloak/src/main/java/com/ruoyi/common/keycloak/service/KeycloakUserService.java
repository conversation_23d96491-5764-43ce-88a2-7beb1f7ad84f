package com.ruoyi.common.keycloak.service;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.SysUser;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * Keycloak用户管理服务
 * 
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "keycloak.enabled", havingValue = "true", matchIfMissing = false)
public class KeycloakUserService {

    private static final Logger log = LoggerFactory.getLogger(KeycloakUserService.class);

    @Value("${keycloak.auth-server-url:http://localhost:8089}")
    private String serverUrl;

    @Value("${keycloak.realm:ruoyi}")
    private String realm;

    @Value("${keycloak.resource:ruoyi-backend}")
    private String clientId;

    @Value("${keycloak.credentials.secret:}")
    private String clientSecret;

    @Value("${keycloak.admin.username:admin}")
    private String adminUsername;

    @Value("${keycloak.admin.password:admin}")
    private String adminPassword;

    private Keycloak keycloak;

    @PostConstruct
    public void initKeycloak() {
        try {
            this.keycloak = KeycloakBuilder.builder()
                    .serverUrl(serverUrl)
                    .realm("master") // 使用master realm进行管理操作
                    .clientId("admin-cli")
                    .username(adminUsername)
                    .password(adminPassword)
                    .build();
            log.info("Keycloak admin client initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Keycloak admin client", e);
        }
    }

    /**
     * 根据用户名获取Keycloak用户
     */
    public UserRepresentation getKeycloakUser(String username) {
        try {
            RealmResource realmResource = keycloak.realm(realm);
            UsersResource usersResource = realmResource.users();
            List<UserRepresentation> users = usersResource.search(username, true);
            
            if (!users.isEmpty()) {
                return users.get(0);
            }
        } catch (Exception e) {
            log.error("Failed to get Keycloak user: {}", username, e);
        }
        return null;
    }

    /**
     * 将Keycloak用户转换为系统用户
     */
    public SysUser convertToSysUser(UserRepresentation keycloakUser) {
        if (keycloakUser == null) {
            return null;
        }

        SysUser sysUser = new SysUser();
        sysUser.setUserName(keycloakUser.getUsername());
        sysUser.setNickName(keycloakUser.getFirstName() + " " + keycloakUser.getLastName());
        sysUser.setEmail(keycloakUser.getEmail());
        sysUser.setStatus(keycloakUser.isEnabled() ? "0" : "1"); // 0正常 1停用
        
        // 设置默认值
        sysUser.setDelFlag("0"); // 0正常 2删除
        sysUser.setUserType("00"); // 00系统用户
        
        return sysUser;
    }

    /**
     * 同步Keycloak用户到本地系统
     */
    public SysUser syncKeycloakUser(String username) {
        try {
            UserRepresentation keycloakUser = getKeycloakUser(username);
            if (keycloakUser != null) {
                return convertToSysUser(keycloakUser);
            }
        } catch (Exception e) {
            log.error("Failed to sync Keycloak user: {}", username, e);
        }
        return null;
    }

    /**
     * 验证用户是否存在于Keycloak
     */
    public boolean userExistsInKeycloak(String username) {
        return getKeycloakUser(username) != null;
    }

    /**
     * 获取用户的角色信息
     */
    public List<String> getUserRoles(String username) {
        try {
            UserRepresentation user = getKeycloakUser(username);
            if (user != null) {
                RealmResource realmResource = keycloak.realm(realm);
                UserResource userResource = realmResource.users().get(user.getId());
                return userResource.roles().realmLevel().listEffective()
                        .stream()
                        .map(role -> role.getName())
                        .collect(java.util.stream.Collectors.toList());
            }
        } catch (Exception e) {
            log.error("Failed to get user roles for: {}", username, e);
        }
        return java.util.Collections.emptyList();
    }
}
