package com.ruoyi.common.keycloak.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * 默认安全配置 - 当Keycloak未启用时使用
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@ConditionalOnProperty(name = "keycloak.enabled", havingValue = "false", matchIfMissing = true)
public class DefaultSecurityConfig extends WebSecurityConfigurerAdapter {

    /**
     * 默认安全配置 - 允许所有请求通过，由网关和业务层处理认证
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .anyRequest().permitAll()
            .and()
            .csrf().disable();
    }
}
