<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi-common</artifactId>
        <version>3.6.5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>ruoyi-common-datasource</artifactId>

    <description>
        ruoyi-common-datasource多数据源
    </description>
    <distributionManagement>
        <repository>
            <id>company-releases</id>
            <url>file:///Users/<USER>/maven_local/</url>
        </repository>
        <snapshotRepository>
            <id>company-snapshots</id>
            <url>file:///Users/<USER>/maven_local/</url>
        </snapshotRepository>
    </distributionManagement>
    <dependencies>

        <!-- Druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>

        <!-- Dynamic DataSource -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${dynamic-ds.version}</version>
        </dependency>

    </dependencies>
</project>