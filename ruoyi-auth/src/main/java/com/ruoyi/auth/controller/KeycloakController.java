package com.ruoyi.auth.controller;

import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.keycloak.service.KeycloakTokenService;
import com.ruoyi.common.keycloak.service.KeycloakUserService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Keycloak认证控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/keycloak")
@ConditionalOnProperty(name = "keycloak.enabled", havingValue = "true", matchIfMissing = false)
public class KeycloakController {

    private static final Logger log = LoggerFactory.getLogger(KeycloakController.class);

    @Autowired
    private KeycloakTokenService keycloakTokenService;

    @Autowired
    private KeycloakUserService keycloakUserService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    /**
     * Keycloak Token验证登录
     */
    @PostMapping("/login")
    public R<?> keycloakLogin(@RequestBody Map<String, String> params) {
        try {
            String accessToken = params.get("access_token");
            String refreshToken = params.get("refresh_token");
            Long expiresIn = Long.valueOf(params.getOrDefault("expires_in", "3600"));

            if (StringUtils.isEmpty(accessToken)) {
                return R.fail("Access token不能为空");
            }

            // 验证Keycloak Token
            if (!keycloakTokenService.validateKeycloakToken(accessToken)) {
                return R.fail("无效的Keycloak Token");
            }

            // 从Keycloak Token中提取用户信息
            LoginUser loginUser = keycloakTokenService.getLoginUserFromKeycloakToken(accessToken);
            if (loginUser == null) {
                return R.fail("无法从Token中获取用户信息");
            }

            // 同步用户信息到本地系统
            syncUserToLocal(loginUser.getUsername());

            // 创建系统Token
            Map<String, Object> tokenResponse = tokenService.createToken(loginUser);
            
            // 添加Keycloak相关信息
            tokenResponse.put("keycloak_access_token", accessToken);
            tokenResponse.put("keycloak_refresh_token", refreshToken);
            tokenResponse.put("keycloak_expires_in", expiresIn);

            log.info("用户{}通过Keycloak登录成功", loginUser.getUsername());
            return R.ok(tokenResponse);

        } catch (Exception e) {
            log.error("Keycloak登录失败", e);
            return R.fail("Keycloak登录失败: " + e.getMessage());
        }
    }

    /**
     * 验证Keycloak Token
     */
    @PostMapping("/validate")
    public R<?> validateToken(HttpServletRequest request) {
        try {
            String token = keycloakTokenService.getKeycloakToken(request);
            if (StringUtils.isEmpty(token)) {
                return R.fail("Token不能为空");
            }

            boolean isValid = keycloakTokenService.validateKeycloakToken(token);
            if (isValid) {
                LoginUser loginUser = keycloakTokenService.getLoginUserFromKeycloakToken(token);
                return R.ok(loginUser);
            } else {
                return R.fail("Token验证失败");
            }
        } catch (Exception e) {
            log.error("Token验证失败", e);
            return R.fail("Token验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/userinfo")
    public R<?> getUserInfo(HttpServletRequest request) {
        try {
            String token = keycloakTokenService.getKeycloakToken(request);
            if (StringUtils.isEmpty(token)) {
                return R.fail("Token不能为空");
            }

            LoginUser loginUser = keycloakTokenService.getLoginUserFromKeycloakToken(token);
            if (loginUser != null) {
                return R.ok(loginUser);
            } else {
                return R.fail("无法获取用户信息");
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return R.fail("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 同步用户到本地系统
     */
    private void syncUserToLocal(String username) {
        try {
            // 检查用户是否已存在于本地系统
            // 如果不存在，则从Keycloak同步用户信息
            if (!keycloakUserService.userExistsInKeycloak(username)) {
                log.warn("用户{}在Keycloak中不存在", username);
                return;
            }

            // 这里可以添加更复杂的用户同步逻辑
            // 例如：同步用户角色、权限等
            log.info("用户{}同步完成", username);
        } catch (Exception e) {
            log.error("同步用户{}失败", username, e);
        }
    }
}
