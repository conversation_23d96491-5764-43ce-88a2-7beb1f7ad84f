# Keycloak Configuration
keycloak:
  enabled: ${<PERSON><PERSON><PERSON><PERSON><PERSON>K_ENABLED:false}
  auth-server-url: ${KE<PERSON><PERSON>OAK_AUTH_SERVER_URL:http://localhost:8089}
  realm: ${KEYCLOAK_REALM:ruoyi}
  resource: ${K<PERSON><PERSON><PERSON>OAK_CLIENT_ID:ruoyi-backend}
  credentials:
    secret: ${KEYCLOAK_CLIENT_SECRET:}
  use-resource-role-mappings: true
  bearer-only: true
  admin:
    username: ${KEYCLOAK_ADMIN_USERNAME:admin}
    password: ${KEYCLOAK_ADMIN_PASSWORD:admin}

# Spring Security Configuration
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}
          jwk-set-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/certs

# Logging Configuration
logging:
  level:
    org.keycloak: INFO
    com.ruoyi.common.keycloak: DEBUG
