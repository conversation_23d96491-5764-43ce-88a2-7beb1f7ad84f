#!/bin/bash

# Test Logout Functionality Script
# This script helps debug logout issues

echo "🔍 Testing Logout Functionality"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# Configuration
GATEWAY_URL="http://localhost:8080"
AUTH_URL="http://localhost:9200"
NACOS_URL="http://localhost:8848"

# Test functions
test_service_health() {
    local service_name=$1
    local url=$2
    
    print_test "Testing $service_name health..."
    
    if curl -s -f "$url/actuator/health" > /dev/null; then
        print_status "$service_name is healthy ✓"
        return 0
    else
        print_error "$service_name is not responding ✗"
        return 1
    fi
}

test_service_discovery() {
    print_test "Testing service discovery..."
    
    # Check if ruoyi-auth is registered in Nacos
    local response=$(curl -s "$NACOS_URL/nacos/v1/ns/instance/list?serviceName=ruoyi-auth")
    
    if echo "$response" | grep -q "ruoyi-auth"; then
        print_status "ruoyi-auth service is registered in Nacos ✓"
        return 0
    else
        print_error "ruoyi-auth service is not registered in Nacos ✗"
        print_warning "Response: $response"
        return 1
    fi
}

test_gateway_routes() {
    print_test "Testing gateway routes..."
    
    # Check if gateway has routes configured
    local routes=$(curl -s "$GATEWAY_URL/actuator/gateway/routes" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        print_status "Gateway routes endpoint is accessible ✓"
        
        if echo "$routes" | grep -q "ruoyi-auth"; then
            print_status "ruoyi-auth route is configured ✓"
        else
            print_warning "ruoyi-auth route might not be configured"
            echo "Available routes:"
            echo "$routes" | jq -r '.[].route_id' 2>/dev/null || echo "$routes"
        fi
    else
        print_error "Cannot access gateway routes endpoint ✗"
    fi
}

test_direct_logout() {
    print_test "Testing direct logout (auth service)..."
    
    # Test logout endpoint directly on auth service
    local response=$(curl -s -w "%{http_code}" -X DELETE "$AUTH_URL/logout" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        print_status "Direct logout endpoint is accessible ✓"
    else
        print_error "Direct logout endpoint returned HTTP $response ✗"
    fi
}

test_gateway_logout() {
    print_test "Testing logout through gateway..."
    
    # Test logout endpoint through gateway
    local response=$(curl -s -w "%{http_code}" -X DELETE "$GATEWAY_URL/logout" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        print_status "Gateway logout endpoint is accessible ✓"
    else
        print_error "Gateway logout endpoint returned HTTP $response ✗"
        
        if [ "$response" = "404" ]; then
            print_warning "This suggests the route is not configured properly"
        elif [ "$response" = "000" ]; then
            print_warning "This suggests the service is not reachable"
        fi
    fi
}

test_with_token() {
    local token=$1
    
    if [ -z "$token" ]; then
        print_warning "No token provided, skipping authenticated tests"
        return
    fi
    
    print_test "Testing logout with token..."
    
    # Test with actual token
    local response=$(curl -s -w "%{http_code}" -X DELETE "$GATEWAY_URL/logout" \
        -H "Authorization: Bearer $token" -o /dev/null)
    
    if [ "$response" = "200" ]; then
        print_status "Logout with token successful ✓"
    else
        print_error "Logout with token failed, HTTP $response ✗"
    fi
}

check_nacos_config() {
    print_test "Checking Nacos configuration..."
    
    # Check if there's a gateway configuration in Nacos
    local config=$(curl -s "$NACOS_URL/nacos/v1/cs/configs?dataId=ruoyi-gateway-dev.yml&group=DEFAULT_GROUP")
    
    if [ $? -eq 0 ] && [ -n "$config" ]; then
        print_status "Found gateway configuration in Nacos ✓"
        
        if echo "$config" | grep -q "logout"; then
            print_status "Logout route found in Nacos config ✓"
        else
            print_warning "Logout route not found in Nacos config"
        fi
    else
        print_warning "No gateway configuration found in Nacos"
    fi
}

# Main test execution
main() {
    echo
    print_status "Starting logout functionality tests..."
    echo
    
    # Basic health checks
    test_service_health "Gateway" "$GATEWAY_URL"
    test_service_health "Auth Service" "$AUTH_URL"
    echo
    
    # Service discovery
    test_service_discovery
    echo
    
    # Gateway configuration
    test_gateway_routes
    echo
    
    # Nacos configuration
    check_nacos_config
    echo
    
    # Endpoint tests
    test_direct_logout
    test_gateway_logout
    echo
    
    # Token test (if provided)
    if [ -n "$1" ]; then
        test_with_token "$1"
        echo
    fi
    
    # Summary and recommendations
    echo "📋 Troubleshooting Recommendations:"
    echo "=================================="
    echo
    echo "1. If auth service is not healthy:"
    echo "   - Check if ruoyi-auth service is running"
    echo "   - Check logs: docker logs ruoyi-auth (if using Docker)"
    echo
    echo "2. If service discovery fails:"
    echo "   - Check Nacos console: $NACOS_URL/nacos"
    echo "   - Verify service registration"
    echo
    echo "3. If gateway routes are missing:"
    echo "   - Check local config: ruoyi-gateway/src/main/resources/application.yml"
    echo "   - Check Nacos config: ruoyi-gateway-dev.yml"
    echo
    echo "4. If logout fails through gateway:"
    echo "   - Check gateway logs for routing errors"
    echo "   - Verify whitelist configuration"
    echo
    echo "5. To test with a real token:"
    echo "   - Login first to get a token"
    echo "   - Run: $0 YOUR_TOKEN"
    echo
    print_status "Test completed! 🎯"
}

# Run tests
main "$@"
