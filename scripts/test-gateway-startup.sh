#!/bin/bash

# Test Gateway Startup Script
# This script tests if the gateway can start without servlet conflicts

echo "🚀 Testing Gateway Startup"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# Configuration
GATEWAY_DIR="ruoyi-gateway"
TIMEOUT=60

# Test functions
clean_build() {
    print_test "Cleaning and building gateway..."
    
    cd "$GATEWAY_DIR"
    if mvn clean compile -q; then
        print_status "Gateway compiled successfully ✓"
        cd ..
        return 0
    else
        print_error "Gateway compilation failed ✗"
        cd ..
        return 1
    fi
}

check_dependencies() {
    print_test "Checking for servlet dependencies..."
    
    cd "$GATEWAY_DIR"
    local servlet_deps=$(mvn dependency:tree 2>/dev/null | grep -i servlet | grep -v "provided" | head -5)
    
    if [ -n "$servlet_deps" ]; then
        print_warning "Found servlet dependencies:"
        echo "$servlet_deps"
    else
        print_status "No problematic servlet dependencies found ✓"
    fi
    cd ..
}

test_startup() {
    print_test "Testing gateway startup..."
    
    cd "$GATEWAY_DIR"
    
    # Start the gateway in background
    print_status "Starting gateway (timeout: ${TIMEOUT}s)..."
    timeout $TIMEOUT mvn spring-boot:run -q > startup.log 2>&1 &
    local pid=$!
    
    # Wait for startup or failure
    local count=0
    local max_wait=30
    local started=false
    
    while [ $count -lt $max_wait ]; do
        if grep -q "Netty started on port" startup.log 2>/dev/null; then
            print_status "Gateway started successfully with Netty ✓"
            started=true
            break
        elif grep -q "APPLICATION FAILED TO START" startup.log 2>/dev/null; then
            print_error "Gateway failed to start ✗"
            break
        elif grep -q "ServletContext" startup.log 2>/dev/null; then
            print_error "Still trying to use servlet context ✗"
            break
        fi
        
        sleep 2
        count=$((count + 1))
        echo -n "."
    done
    
    echo
    
    # Kill the process
    kill $pid 2>/dev/null
    wait $pid 2>/dev/null
    
    # Show relevant logs
    if [ "$started" = false ]; then
        print_error "Startup failed. Last 20 lines of log:"
        tail -20 startup.log
    fi
    
    cd ..
    return $started
}

analyze_logs() {
    print_test "Analyzing startup logs..."
    
    cd "$GATEWAY_DIR"
    
    if [ -f "startup.log" ]; then
        # Check for specific issues
        if grep -q "NoSuchMethodError.*getVirtualServerName" startup.log; then
            print_error "Still has servlet API version conflict"
        fi
        
        if grep -q "Tomcat" startup.log; then
            print_error "Still trying to use Tomcat instead of Netty"
        fi
        
        if grep -q "Netty" startup.log; then
            print_status "Using Netty web server (correct for Gateway)"
        fi
        
        if grep -q "reactive" startup.log; then
            print_status "Using reactive web application type"
        fi
    fi
    
    cd ..
}

# Main test execution
main() {
    echo
    print_status "Starting gateway startup tests..."
    echo
    
    # Check if we're in the right directory
    if [ ! -d "$GATEWAY_DIR" ]; then
        print_error "Gateway directory not found. Please run from project root."
        exit 1
    fi
    
    # Run tests
    clean_build
    echo
    
    check_dependencies
    echo
    
    test_startup
    local startup_result=$?
    echo
    
    analyze_logs
    echo
    
    # Summary
    echo "📋 Test Summary:"
    echo "==============="
    echo
    
    if [ $startup_result -eq 0 ]; then
        print_status "✅ Gateway startup test PASSED"
        echo
        echo "Next steps:"
        echo "1. Re-enable captcha functionality"
        echo "2. Test with full configuration"
        echo "3. Test authentication flows"
    else
        print_error "❌ Gateway startup test FAILED"
        echo
        echo "Troubleshooting steps:"
        echo "1. Check startup.log in ruoyi-gateway directory"
        echo "2. Look for remaining servlet dependencies"
        echo "3. Verify Spring Boot version compatibility"
        echo "4. Check for conflicting auto-configurations"
    fi
    
    echo
    print_status "Test completed! 🎯"
}

# Run tests
main "$@"
