#!/bin/bash

# RuoYi-Cloud Keycloak Integration Setup Script
# This script helps set up the complete Keycloak integration

set -e

echo "🚀 RuoYi-Cloud Keycloak Integration Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running ✓"
}

# Check if required files exist
check_files() {
    local files=(
        "docker/docker-compose.yml"
        "docker/keycloak/start.sh"
        "docker/keycloak/setup-realm.sh"
        "sql/keycloak_setup.sql"
    )
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    print_status "All required files found ✓"
}

# Build the project
build_project() {
    print_status "Building the project..."
    if mvn clean install -DskipTests; then
        print_status "Project built successfully ✓"
    else
        print_error "Failed to build project"
        exit 1
    fi
}

# Start infrastructure services
start_infrastructure() {
    print_status "Starting infrastructure services (MySQL, Redis, Nacos)..."
    docker-compose up -d ruoyi-mysql ruoyi-redis ruoyi-nacos
    
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check if MySQL is ready
    local retries=0
    while ! docker exec ruoyi-mysql mysqladmin ping -h"localhost" --silent; do
        if [ $retries -eq 10 ]; then
            print_error "MySQL failed to start"
            exit 1
        fi
        print_status "Waiting for MySQL to be ready..."
        sleep 5
        ((retries++))
    done
    print_status "Infrastructure services started ✓"
}

# Setup Keycloak database
setup_keycloak_db() {
    print_status "Setting up Keycloak database..."
    if docker exec -i ruoyi-mysql mysql -uroot -ppassword < sql/keycloak_setup.sql; then
        print_status "Keycloak database setup completed ✓"
    else
        print_error "Failed to setup Keycloak database"
        exit 1
    fi
}

# Start Keycloak
start_keycloak() {
    print_status "Starting Keycloak..."
    docker-compose up -d ruoyi-keycloak
    
    print_status "Waiting for Keycloak to be ready..."
    local retries=0
    while ! curl -f http://localhost:8089/health/ready > /dev/null 2>&1; do
        if [ $retries -eq 20 ]; then
            print_error "Keycloak failed to start"
            exit 1
        fi
        print_status "Waiting for Keycloak to be ready..."
        sleep 10
        ((retries++))
    done
    print_status "Keycloak started successfully ✓"
}

# Setup Keycloak realm
setup_keycloak_realm() {
    print_status "Setting up Keycloak realm and clients..."
    if ./docker/keycloak/setup-realm.sh; then
        print_status "Keycloak realm setup completed ✓"
    else
        print_warning "Keycloak realm setup failed, you may need to configure it manually"
    fi
}

# Main setup function
main() {
    echo
    print_status "Starting Keycloak integration setup..."
    echo
    
    # Run setup steps
    check_docker
    check_files
    
    read -p "Do you want to build the project? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_project
    fi
    
    start_infrastructure
    setup_keycloak_db
    start_keycloak
    setup_keycloak_realm
    
    echo
    print_status "🎉 Keycloak integration setup completed!"
    echo
    echo "Next steps:"
    echo "1. Set environment variables:"
    echo "   export KEYCLOAK_ENABLED=true"
    echo "   export KEYCLOAK_AUTH_SERVER_URL=http://localhost:8089"
    echo "   export KEYCLOAK_REALM=ruoyi"
    echo
    echo "2. Start RuoYi services:"
    echo "   java -jar ruoyi-gateway/target/ruoyi-gateway.jar"
    echo "   java -jar ruoyi-auth/target/ruoyi-auth.jar"
    echo "   java -jar ruoyi-modules/ruoyi-system/target/ruoyi-modules-system.jar"
    echo
    echo "3. Access Keycloak admin console:"
    echo "   URL: http://localhost:8089/admin"
    echo "   Username: admin"
    echo "   Password: admin"
    echo
    echo "4. Test user credentials:"
    echo "   Username: testuser"
    echo "   Password: password"
    echo
    print_status "Setup completed successfully! 🚀"
}

# Run main function
main "$@"
