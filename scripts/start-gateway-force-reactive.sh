#!/bin/bash

# Force Reactive Gateway Startup Script
# This script starts the gateway with explicit JVM arguments to force reactive mode

echo "🚀 Starting Gateway with Forced Reactive Mode"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Configuration
GATEWAY_DIR="ruoyi-gateway"

# Check if we're in the right directory
if [ ! -d "$GATEWAY_DIR" ]; then
    print_error "Gateway directory not found. Please run from project root."
    exit 1
fi

cd "$GATEWAY_DIR"

print_status "Cleaning and compiling..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    print_error "Compilation failed"
    exit 1
fi

print_status "Starting gateway with forced reactive configuration..."

# JVM arguments to force reactive mode and exclude servlet
JVM_ARGS=(
    "-Dspring.main.web-application-type=reactive"
    "-Dspring.main.allow-bean-definition-overriding=true"
    "-Dserver.port=8080"
    "-Dlogging.level.org.springframework.boot.autoconfigure=DEBUG"
    "-Dlogging.level.org.springframework.boot.web=DEBUG"
    "-Dspring.autoconfigure.exclude=org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration,org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration,org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration"
)

# Maven arguments
MAVEN_ARGS=(
    "spring-boot:run"
    "-Dspring-boot.run.jvmArguments='${JVM_ARGS[*]}'"
    "-Dspring-boot.run.profiles=dev"
)

print_status "JVM Arguments: ${JVM_ARGS[*]}"
print_status "Maven Arguments: ${MAVEN_ARGS[*]}"
echo

print_warning "If this fails, the issue is fundamental and requires alternative solutions."
echo

# Start the application
mvn "${MAVEN_ARGS[@]}"

cd ..
