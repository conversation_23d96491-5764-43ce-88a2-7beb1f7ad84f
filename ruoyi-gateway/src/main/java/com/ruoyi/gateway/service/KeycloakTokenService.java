package com.ruoyi.gateway.service;

import com.ruoyi.common.core.utils.StringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Gateway Keycloak Token服务 - 简化版，不依赖Spring Security
 * 
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "keycloak.enabled", havingValue = "true", matchIfMissing = false)
public class KeycloakTokenService {

    private static final Logger log = LoggerFactory.getLogger(KeycloakTokenService.class);

    @Value("${keycloak.realm:ruoyi}")
    private String realm;

    /**
     * 检查是否为Keycloak Token
     */
    public boolean isKeycloakToken(String token) {
        try {
            if (StringUtils.isEmpty(token) || !token.startsWith("eyJ")) {
                return false;
            }
            
            // 简单解析JWT header来判断是否为Keycloak token
            Claims claims = parseTokenUnsafe(token);
            if (claims != null) {
                String issuer = claims.getIssuer();
                return issuer != null && issuer.contains("/realms/" + realm);
            }
        } catch (Exception e) {
            log.debug("Token is not a valid Keycloak token", e);
        }
        return false;
    }

    /**
     * 从Keycloak Token中提取用户信息（不验证签名）
     * 注意：这里不验证签名，仅用于网关路由，实际验证在后端服务进行
     */
    public Claims parseKeycloakToken(String token) {
        try {
            return parseTokenUnsafe(token);
        } catch (Exception e) {
            log.error("Failed to parse Keycloak token", e);
            return null;
        }
    }

    /**
     * 不安全的token解析（不验证签名）
     * 仅用于提取基本信息，实际验证在后端进行
     */
    private Claims parseTokenUnsafe(String token) {
        try {
            // 分割token，只解析payload部分
            String[] chunks = token.split("\\.");
            if (chunks.length != 3) {
                return null;
            }
            
            // 使用不验证签名的方式解析
            return Jwts.parser()
                    .parseClaimsJwt(chunks[0] + "." + chunks[1] + ".")
                    .getBody();
        } catch (Exception e) {
            log.debug("Failed to parse token unsafely", e);
            return null;
        }
    }

    /**
     * 从Claims中提取用户名
     */
    public String getUsernameFromClaims(Claims claims) {
        if (claims == null) {
            return null;
        }
        
        // 尝试不同的用户名字段
        String username = claims.get("preferred_username", String.class);
        if (StringUtils.isEmpty(username)) {
            username = claims.get("username", String.class);
        }
        if (StringUtils.isEmpty(username)) {
            username = claims.getSubject();
        }
        
        return username;
    }

    /**
     * 从Claims中提取用户ID
     */
    public String getUserIdFromClaims(Claims claims) {
        if (claims == null) {
            return null;
        }
        
        // 使用subject的hash作为用户ID
        String subject = claims.getSubject();
        return subject != null ? String.valueOf(subject.hashCode()) : null;
    }
}
