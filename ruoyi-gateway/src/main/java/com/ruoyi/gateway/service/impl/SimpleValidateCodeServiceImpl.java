package com.ruoyi.gateway.service.impl;

import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.exception.CaptchaException;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.gateway.service.ValidateCodeService;

/**
 * 简单验证码实现 - 不依赖Kaptcha，用于测试启动
 *
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(name = "security.captcha.enabled", havingValue = "false", matchIfMissing = true)
public class SimpleValidateCodeServiceImpl implements ValidateCodeService
{
    @Autowired
    private RedisService redisService;

    /**
     * 生成验证码 - 简化版本
     */
    @Override
    public AjaxResult createCaptcha() throws IOException, CaptchaException
    {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("captchaEnabled", false);
        ajax.put("uuid", IdUtils.simpleUUID());
        ajax.put("img", ""); // Empty image for testing
        return ajax;
    }

    /**
     * 校验验证码 - 简化版本
     */
    @Override
    public void checkCaptcha(String code, String uuid) throws CaptchaException
    {
        // For testing, always pass validation
        // In production, this would be replaced with proper captcha validation
    }
}
