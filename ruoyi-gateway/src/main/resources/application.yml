# Keycloak Configuration
keycloak:
  enabled: ${<PERSON><PERSON><PERSON><PERSON><PERSON>K_ENABLED:false}
  auth-server-url: ${KE<PERSON><PERSON>OAK_AUTH_SERVER_URL:http://localhost:8089}
  realm: ${KEYCLOAK_REALM:ruoyi}
  resource: ${KEYCLOAK_CLIENT_ID:ruoyi-backend}

# Security Configuration
security:
  ignore:
    whites:
      - /auth/**
      - /login
      - /logout
      - /refresh
      - /captchaImage
      - /actuator/**
      - /keycloak/**

# Gateway Configuration
spring:
  cloud:
    gateway:
      routes:
        # Keycloak routes
        - id: keycloak-auth
          uri: http://localhost:8089
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # Auth service routes - traditional endpoints
        - id: ruoyi-auth
          uri: lb://ruoyi-auth
          predicates:
            - Path=/login,/logout,/refresh,/register
        # Auth service routes with Keycloak support
        - id: ruoyi-auth-keycloak
          uri: lb://ruoyi-auth
          predicates:
            - Path=/keycloak/**

# Logging Configuration
logging:
  level:
    com.ruoyi.gateway.filter.AuthFilter: DEBUG
    com.ruoyi.common.keycloak: DEBUG
