# Servlet API Conflict Fix

## Problem: NoSuchMethodError - javax.servlet.ServletContext.getVirtualServerName()

### Root Cause Analysis

The error occurred because of a **Servlet API version conflict**:

1. **Tomcat 9.0.102** expects **Servlet API 4.0+** (which has `getVirtualServerName()` method)
2. **Kaptcha dependency** pulls in **Servlet API 2.5** (which doesn't have this method)
3. **Spring Cloud Gateway** should use **Netty** (reactive), not **Tomcat** (servlet)

### Error Chain
```
Kaptcha → Servlet API 2.5 → Tom<PERSON> tries to use Servlet API 4.0 methods → NoSuchMethodError
```

## Solution Applied

### 1. **Excluded Problematic Dependencies**

#### Spring Cloud Gateway
```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-gateway</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

#### Kaptcha (Captcha Library)
```xml
<dependency>
    <groupId>pro.fessional</groupId>
    <artifactId>kaptcha</artifactId>
    <exclusions>
        <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

#### RuoYi Common Redis
```xml
<dependency>
    <groupId>com.ruoyi</groupId>
    <artifactId>ruoyi-common-redis</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </exclusion>
        <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 2. **Added Explicit WebFlux Dependency**

```xml
<!-- Ensure WebFlux instead of Web MVC -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>
```

### 3. **Updated Configuration**

Added captcha configuration and `/code` endpoint to whitelist:

```yaml
security:
  ignore:
    whites:
      - /code  # Captcha endpoint
      # ... other endpoints
  captcha:
    enabled: true
    type: char
```

## Architecture After Fix

### Before (Problematic):
```
Spring Cloud Gateway → Kaptcha → Servlet API 2.5 → Tomcat 9.0.102 → CONFLICT ❌
```

### After (Fixed):
```
Spring Cloud Gateway → WebFlux → Netty → Kaptcha (without servlet) → SUCCESS ✅
```

## Key Changes Summary

### ✅ **Dependencies Fixed**
- Excluded all servlet-based dependencies
- Added explicit WebFlux dependency
- Maintained Kaptcha functionality without servlet conflicts

### ✅ **Web Server**
- **Before**: Tomcat (servlet-based) - WRONG for Gateway
- **After**: Netty (reactive) - CORRECT for Gateway

### ✅ **Captcha System**
- Maintained existing WebFlux-compatible captcha implementation
- No changes needed to handlers or services
- Added configuration for easy enable/disable

## Testing the Fix

### 1. **Clean Build**
```bash
cd ruoyi-gateway
mvn clean compile
```

### 2. **Start Gateway**
```bash
mvn spring-boot:run
```

### 3. **Verify Web Server**
Look for this in logs:
```
Netty started on port(s): 8080  ✅ (CORRECT - Reactive)
```

NOT this:
```
Tomcat started on port(s): 8080  ❌ (WRONG - Servlet)
```

### 4. **Test Endpoints**
```bash
# Health check
curl http://localhost:8080/actuator/health

# Captcha generation
curl http://localhost:8080/code

# Gateway routes
curl http://localhost:8080/actuator/gateway/routes
```

## Verification Checklist

### ✅ **Startup Success**
- [ ] Gateway starts without errors
- [ ] Uses Netty (not Tomcat)
- [ ] No servlet API conflicts

### ✅ **Functionality**
- [ ] Captcha generation works (`/code`)
- [ ] Gateway routing works
- [ ] Authentication flow works
- [ ] Keycloak integration works (if enabled)

### ✅ **Performance**
- [ ] Reactive programming benefits
- [ ] No blocking servlet operations
- [ ] Proper WebFlux pipeline

## Common Issues After Fix

### Issue 1: Captcha Not Working
**Solution**: Check if captcha is enabled in configuration:
```yaml
security:
  captcha:
    enabled: true
```

### Issue 2: Still Getting Servlet Errors
**Solution**: 
1. Clean build: `mvn clean install`
2. Check for other servlet dependencies: `mvn dependency:tree | grep servlet`
3. Exclude any remaining servlet dependencies

### Issue 3: Gateway Routes Not Working
**Solution**: Verify Nacos configuration and service registration

## Benefits of This Fix

### ✅ **Compatibility**
- Spring Cloud Gateway works as intended
- No servlet/reactive conflicts
- Proper dependency management

### ✅ **Performance**
- Uses Netty (reactive) instead of Tomcat (blocking)
- Better resource utilization
- Improved scalability

### ✅ **Maintainability**
- Clear separation of concerns
- Proper reactive programming model
- Future-proof architecture

The gateway should now start successfully with Netty as the web server and maintain all existing functionality including captcha generation and Keycloak integration!
