-- Keycloak Database Setup Script
-- This script creates the Keycloak database and user

-- Create Keycloak database
CREATE DATABASE IF NOT EXISTS keycloak CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create Keycloak user (optional, can use root)
-- CREATE USER IF NOT EXISTS 'keycloak'@'%' IDENTIFIED BY 'keycloak_password';
-- GRANT ALL PRIVILEGES ON keycloak.* TO 'keycloak'@'%';

-- Use the keycloak database
USE keycloak;

-- Note: Keycloak will automatically create its tables on first startup
-- This script just ensures the database exists

-- Optional: Create indexes for better performance (Keycloak will create these automatically)
-- These are just examples of what Keycloak typically creates

-- Flush privileges
FLUSH PRIVILEGES;

-- Display success message
SELECT 'Keycloak database setup completed successfully!' as message;
