<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.orderId"
        placeholder="订单ID"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.productType"
        placeholder="产品类型"
        clearable
        style="width: 130px"
        class="filter-item"
      >
        <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 130px"
        class="filter-item"
      >
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
        style="width: 240px"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="success" icon="el-icon-download" @click="handleDownload">
        导出
      </el-button>
      <el-button 
        class="filter-item" 
        style="margin-left: 10px;" 
        type="primary" 
        icon="el-icon-setting" 
        @click="dialogVisible = true">
        列设置
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      table-layout="fixed"
      :default-sort="{prop: 'date', order: 'descending'}"
      @sort-change="sortChange"
    >
      <template v-for="column in displayColumns">
        <el-table-column
          :key="column.prop"
          :label="column.label"
          :prop="column.prop"
          sortable="custom"
          align="center"
          :width="column.width"
        >
          <template slot-scope="{row}">
            <template v-if="column.prop === 'productType'">
              <el-tag :type="row.productType | productTypeFilter">{{ row.productType }}</el-tag>
            </template>
            <template v-else-if="column.prop === 'status'">
              <el-tag :type="row.status | statusFilter">{{ row.status }}</el-tag>
            </template>
            <template v-else-if="column.prop === 'price'">
              <span>{{ row.price | formatPrice }}</span>
            </template>
            <template v-else-if="column.prop === 'operation'">
              <el-button type="text" size="small" @click="handleDetail(row)">查看</el-button>
            </template>
            <template v-else>
              <span>{{ row[column.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    
    <el-dialog title="列设置" :visible.sync="dialogVisible" width="30%">
      <el-checkbox-group v-model="checkColumnList">
        <draggable v-model="allColumns" handle=".drag-handle">
          <div v-for="column in allColumns" :key="column.prop" class="column-item">
            <span class="drag-handle"><i class="el-icon-rank"></i></span>
            <el-checkbox :label="column.prop">{{ column.label }}</el-checkbox>
          </div>
        </draggable>
      </el-checkbox-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveColumnSettings">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import draggable from 'vuedraggable'

export default {
  name: 'DataTable',
  components: { Pagination, draggable },
  filters: {
    statusFilter(status) {
      const statusMap = {
        'completed': 'success',
        'pending': 'warning',
        'rejected': 'danger'
      }
      return statusMap[status]
    },
    productTypeFilter(type) {
      const typeMap = {
        'Accu': 'primary',
        'Decu': 'success',
        'FCN': 'info'
      }
      return typeMap[type]
    },
    formatPrice(price) {
      return '$' + price.toFixed(2)
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        orderId: undefined,
        productType: undefined,
        status: undefined,
        sort: 'date',
        order: 'descending'
      },
      dateRange: [],
      productTypeOptions: [
        { label: 'Accu', value: 'Accu' },
        { label: 'Decu', value: 'Decu' },
        { label: 'FCN', value: 'FCN' }
      ],
      statusOptions: [
        { label: '已完成', value: 'completed' },
        { label: '处理中', value: 'pending' },
        { label: '已拒绝', value: 'rejected' }
      ],
      dialogVisible: false,
      allColumns: [
        { label: '日期', prop: 'date', width: '120' },
        { label: '时间', prop: 'time', width: '120' },
        { label: '订单ID', prop: 'orderId', width: '150' },
        { label: '产品类型', prop: 'productType', width: '120' },
        { label: '价格', prop: 'price', width: '120' },
        { label: '交易对手', prop: 'counterparty', width: '150' },
        { label: '状态', prop: 'status', width: '120' },
        { label: '操作', prop: 'operation', width: '120' }
      ],
      checkColumnList: ['date', 'time', 'orderId', 'productType', 'price', 'counterparty', 'status', 'operation']
    }
  },
  computed: {
    displayColumns() {
      return this.allColumns.filter(column => this.checkColumnList.includes(column.prop));
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      // 模拟API调用
      setTimeout(() => {
        // Get the mock data
        let data = this.generateMockData()
        
        // Apply sorting if sort parameters exist
        if (this.listQuery.sort) {
          const sortProp = this.listQuery.sort
          const sortOrder = this.listQuery.order
          
          data.sort((a, b) => {
            let aValue = a[sortProp]
            let bValue = b[sortProp]
            
            // Handle special cases for different data types
            if (sortProp === 'price') {
              aValue = parseFloat(aValue)
              bValue = parseFloat(bValue)
            } else if (sortProp === 'date') {
              aValue = new Date(aValue).getTime()
              bValue = new Date(bValue).getTime()
            }
            
            // Apply sort direction
            if (sortOrder === 'ascending') {
              return aValue > bValue ? 1 : -1
            } else {
              return aValue < bValue ? 1 : -1
            }
          })
        }
        
        this.list = data
        this.total = 100
        this.listLoading = false
      }, 500)
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      this.listQuery.sort = prop
      this.listQuery.order = order
      this.getList()
    },
    handleDetail(row) {
      this.$message({
        message: '查看订单详情: ' + row.orderId,
        type: 'info'
      })
    },
    handleDownload() {
      this.$message({
        message: '正在导出数据...',
        type: 'success'
      })
      // 实际导出逻辑
    },
    saveColumnSettings() {
      this.dialogVisible = false
      this.$message({
        message: '列设置已保存',
        type: 'success'
      })
    },
    generateMockData() {
      const mockData = []
      const productTypes = ['Accu', 'Decu', 'FCN']
      const statuses = ['completed', 'pending', 'rejected']
      const counterparties = ['Company A', 'Company B', 'Company C', 'Company D', 'Company E']
      
      for (let i = 0; i < 50; i++) {
        const date = new Date()
        date.setDate(date.getDate() - Math.floor(Math.random() * 30))
        
        mockData.push({
          date: date.toISOString().split('T')[0],
          time: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
          orderId: `ORD-${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
          productType: productTypes[Math.floor(Math.random() * productTypes.length)],
          price: Math.random() * 10000,
          counterparty: counterparties[Math.floor(Math.random() * counterparties.length)],
          status: statuses[Math.floor(Math.random() * statuses.length)]
        })
      }
      
      return mockData
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    margin-right: 10px;
    margin-bottom: 10px;
  }
}

.column-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  
  .drag-handle {
    cursor: move;
    margin-right: 10px;
    color: #909399;
  }
}

/* Add this to ensure table columns are properly sized */
::v-deep .el-table {
  table-layout: fixed;
  
  .el-table__header th,
  .el-table__body td {
    padding: 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
