<template>
  <div class="app-container">
    <div class="dashboard-container">
      <h1>数据看板</h1>
      <div class="dashboard-content">
        <!-- 这里可以添加数据看板的内容 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <div slot="header">
                <span>总用户数</span>
              </div>
              <div class="card-panel">
                <div class="card-panel-icon">
                  <svg-icon icon-class="peoples" />
                </div>
                <div class="card-panel-description">
                  <div class="card-panel-text">用户总数</div>
                  <count-to :start-val="0" :end-val="1234" :duration="2000" class="card-panel-num" />
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <div slot="header">
                <span>今日访问</span>
              </div>
              <div class="card-panel">
                <div class="card-panel-icon">
                  <svg-icon icon-class="people" />
                </div>
                <div class="card-panel-description">
                  <div class="card-panel-text">访问人数</div>
                  <count-to :start-val="0" :end-val="321" :duration="2000" class="card-panel-num" />
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <div slot="header">
                <span>系统消息</span>
              </div>
              <div class="card-panel">
                <div class="card-panel-icon">
                  <svg-icon icon-class="message" />
                </div>
                <div class="card-panel-description">
                  <div class="card-panel-text">未读消息</div>
                  <count-to :start-val="0" :end-val="12" :duration="2000" class="card-panel-num" />
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'

export default {
  name: 'Dashboard',
  components: {
    CountTo
  },
  data() {
    return {
      // 数据
    }
  },
  methods: {
    // 方法
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-content {
    margin-top: 20px;
  }
}
.card-panel {
  display: flex;
  align-items: center;
  height: 108px;
  
  &-icon {
    font-size: 48px;
    padding: 0 20px;
    color: #409EFF;
  }
  
  &-description {
    margin-left: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  &-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 12px;
  }
  
  &-num {
    font-size: 24px;
    font-weight: bold;
  }
}
</style>