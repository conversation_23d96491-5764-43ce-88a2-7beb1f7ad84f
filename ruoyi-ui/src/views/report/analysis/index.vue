<template>
  <div class="app-container">
    <div class="analysis-container">
      <h1>数据分析</h1>
      <div class="filter-container">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          style="width: 360px"
        />
        <el-button type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <div slot="header">
              <span>用户增长趋势</span>
            </div>
            <div class="chart-wrapper">
              <div ref="userGrowthChart" style="width: 100%; height: 350px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header">
              <span>用户分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="userDistributionChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header">
              <span>访问来源</span>
            </div>
            <div class="chart-wrapper">
              <div ref="accessSourceChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// 需要安装 echarts
// npm install echarts --save
export default {
  name: 'Analysis',
  data() {
    return {
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      charts: {
        userGrowth: null,
        userDistribution: null,
        accessSource: null
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  beforeDestroy() {
    this.disposeCharts()
  },
  methods: {
    handleFilter() {
      // 处理筛选逻辑
      this.$message.success('数据已更新')
      this.initCharts()
    },
    initCharts() {
      // 需要引入 echarts
      const echarts = require('echarts')
      
      // 用户增长趋势图
      this.charts.userGrowth = echarts.init(this.$refs.userGrowthChart)
      this.charts.userGrowth.setOption({
        title: {
          text: '用户增长趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['新增用户', '活跃用户']
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '新增用户',
          type: 'line',
          data: [120, 132, 101, 134, 90, 230, 210]
        }, {
          name: '活跃用户',
          type: 'line',
          data: [220, 182, 191, 234, 290, 330, 310]
        }]
      })
      
      // 用户分布图
      this.charts.userDistribution = echarts.init(this.$refs.userDistributionChart)
      this.charts.userDistribution.setOption({
        title: {
          text: '用户地区分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [{
          name: '用户分布',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 1048, name: '北京' },
            { value: 735, name: '上海' },
            { value: 580, name: '广州' },
            { value: 484, name: '深圳' },
            { value: 300, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      })
      
      // 访问来源图
      this.charts.accessSource = echarts.init(this.$refs.accessSourceChart)
      this.charts.accessSource.setOption({
        title: {
          text: '访问来源',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [{
          name: '访问来源',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 1048, name: '搜索引擎' },
            { value: 735, name: '直接访问' },
            { value: 580, name: '邮件营销' },
            { value: 484, name: '联盟广告' },
            { value: 300, name: '视频广告' }
          ]
        }]
      })
    },
    disposeCharts() {
      // 销毁图表，避免内存泄漏
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].dispose()
          this.charts[key] = null
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis {
  &-container {
    margin: 30px;
  }
}
.filter-container {
  margin-bottom: 20px;
}
.chart-wrapper {
  padding: 10px;
}
</style>