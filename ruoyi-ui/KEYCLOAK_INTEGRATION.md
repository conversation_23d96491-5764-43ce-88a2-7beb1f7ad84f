# Keycloak Integration for RuoYi-UI

This document describes the Keycloak integration implemented in the RuoYi-UI project.

## Overview

The integration supports both traditional username/password authentication and Keycloak SSO authentication. Users can choose between the two authentication methods on the login page.

## Configuration

### Environment Variables

Configure the following environment variables in your `.env.development` file:

```bash
# Keycloak配置
VUE_APP_KEYCLOAK_URL = 'http://localhost:8080/auth'
VUE_APP_KEYCLOAK_REALM = 'ruoyi'
VUE_APP_KEYCLOAK_CLIENT_ID = 'ruoyi-frontend'
```

### Keycloak Server Setup

1. Install and start Keycloak server
2. Create a realm named `ruoyi`
3. Create a client named `ruoyi-frontend` with the following settings:
   - Client Protocol: openid-connect
   - Access Type: public
   - Valid Redirect URIs: `http://localhost:80/*`
   - Web Origins: `http://localhost:80`

## Features

### Dual Authentication Support
- Traditional username/password login
- Keycloak SSO login
- Automatic authentication mode detection

### Token Management
- Automatic token refresh
- Secure token storage
- Token expiration handling

### User Management
- User info extraction from Keycloak tokens
- Role and permission mapping
- Seamless integration with existing user store

### Logout Handling
- Proper Keycloak logout
- Session cleanup
- Redirect to login page

## Files Modified/Created

### New Files
- `src/config/keycloak.js` - Keycloak configuration
- `src/services/keycloak.js` - Keycloak service wrapper
- `public/silent-check-sso.html` - Silent SSO check page

### Modified Files
- `src/store/modules/user.js` - Added Keycloak actions
- `src/utils/auth.js` - Enhanced auth utilities
- `src/views/login.vue` - Added Keycloak login button
- `src/permission.js` - Updated route guards
- `src/utils/request.js` - Updated HTTP interceptors
- `src/main.js` - Added Keycloak initialization
- `src/layout/components/Navbar.vue` - Updated logout handling

## Usage

1. Start your Keycloak server
2. Configure the environment variables
3. Run the application: `npm run dev`
4. On the login page, choose between:
   - Traditional login (username/password)
   - Keycloak login (SSO)

## Troubleshooting

### Common Issues

1. **Keycloak initialization fails**: Check if Keycloak server is running and accessible
2. **CORS errors**: Ensure Web Origins are properly configured in Keycloak client
3. **Token refresh issues**: Verify client configuration and token settings

### Debug Mode

Enable debug logging by adding to your browser console:
```javascript
localStorage.setItem('debug', 'keycloak:*')
```

## Security Considerations

- Tokens are stored securely in cookies
- Automatic token refresh prevents session expiration
- Proper logout ensures complete session cleanup
- PKCE (Proof Key for Code Exchange) is enabled for enhanced security
