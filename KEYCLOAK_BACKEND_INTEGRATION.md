# Keycloak Backend Integration for RuoYi-Cloud

This document describes the complete Keycloak integration implemented in the RuoYi-Cloud backend services.

## Overview

The integration provides:
- Dual authentication support (traditional JWT + Keycloak SSO)
- Gateway-level token validation for both token types
- User synchronization between Keycloak and local system
- Seamless integration with existing RuoYi security framework

## Architecture

```
Frontend (ruoyi-ui) 
    ↓ (Keycloak Token)
Gateway (ruoyi-gateway) 
    ↓ (Validated Token + User Headers)
Auth Service (ruoyi-auth)
    ↓ (User Info)
Backend Services (ruoyi-modules)
```

## Components Added

### 1. ruoyi-common-keycloak Module
- **KeycloakConfig**: Spring Security configuration for Keycloak
- **KeycloakTokenService**: Token validation and user extraction
- **KeycloakUserService**: User synchronization with Keycloak

### 2. Enhanced Gateway (ruoyi-gateway)
- **AuthFilter**: Updated to support both JWT and Keycloak tokens
- Automatic token type detection
- Keycloak token validation

### 3. Enhanced Auth Service (ruoyi-auth)
- **KeycloakController**: New endpoints for Keycloak authentication
- Token validation and user info endpoints
- Integration with existing TokenService

## Configuration

### Environment Variables

Set these environment variables to enable Keycloak:

```bash
# Enable Keycloak integration
KEYCLOAK_ENABLED=true

# Keycloak server configuration
KEYCLOAK_AUTH_SERVER_URL=http://localhost:8089
KEYCLOAK_REALM=ruoyi
KEYCLOAK_CLIENT_ID=ruoyi-backend
KEYCLOAK_CLIENT_SECRET=your-client-secret

# Keycloak admin credentials
KEYCLOAK_ADMIN_USERNAME=admin
KEYCLOAK_ADMIN_PASSWORD=admin
```

### Application Configuration

The integration uses these configuration files:
- `ruoyi-auth/src/main/resources/application.yml`
- `ruoyi-gateway/src/main/resources/application.yml`

## Setup Instructions

### 1. Build the Project

```bash
mvn clean install
```

### 2. Start Infrastructure Services

```bash
# Start MySQL, Redis, Nacos
docker-compose up -d ruoyi-mysql ruoyi-redis ruoyi-nacos
```

### 3. Setup Keycloak Database

```bash
# Run the Keycloak database setup
mysql -u root -p < sql/keycloak_setup.sql
```

### 4. Start Keycloak

```bash
# Start Keycloak service
docker-compose up -d ruoyi-keycloak

# Or use the standalone script
./docker/keycloak/start.sh
```

### 5. Configure Keycloak Realm

```bash
# Wait for Keycloak to start, then run setup
./docker/keycloak/setup-realm.sh
```

### 6. Start RuoYi Services

```bash
# Start with Keycloak enabled
export KEYCLOAK_ENABLED=true
export KEYCLOAK_AUTH_SERVER_URL=http://localhost:8089
export KEYCLOAK_REALM=ruoyi

# Start services
java -jar ruoyi-gateway/target/ruoyi-gateway.jar
java -jar ruoyi-auth/target/ruoyi-auth.jar
java -jar ruoyi-modules/ruoyi-system/target/ruoyi-modules-system.jar
```

## API Endpoints

### Keycloak Authentication Endpoints

#### POST /keycloak/login
Login with Keycloak token:
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "expires_in": 3600
}
```

#### POST /keycloak/validate
Validate Keycloak token:
```bash
curl -H "Authorization: Bearer <keycloak-token>" \
     http://localhost:8080/keycloak/validate
```

#### GET /keycloak/userinfo
Get user information from Keycloak token:
```bash
curl -H "Authorization: Bearer <keycloak-token>" \
     http://localhost:8080/keycloak/userinfo
```

## Token Flow

### Traditional JWT Flow (Existing)
1. User logs in with username/password
2. System generates JWT token
3. Gateway validates JWT token
4. Request forwarded to backend services

### Keycloak SSO Flow (New)
1. User authenticates with Keycloak (frontend)
2. Frontend receives Keycloak access token
3. Frontend sends token to `/keycloak/login`
4. System validates Keycloak token and creates session
5. Gateway recognizes Keycloak tokens and validates them
6. Request forwarded to backend services

## Testing

### 1. Test Traditional Authentication
```bash
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### 2. Test Keycloak Authentication
```bash
# First get Keycloak token (use frontend or direct Keycloak API)
# Then test with the token
curl -X POST http://localhost:8080/keycloak/login \
  -H "Content-Type: application/json" \
  -d '{"access_token": "your-keycloak-token", "expires_in": 3600}'
```

## Troubleshooting

### Common Issues

1. **Keycloak not starting**: Check Docker logs and ensure MySQL is running
2. **Token validation fails**: Verify Keycloak realm and client configuration
3. **CORS errors**: Check Keycloak client web origins configuration
4. **Database connection**: Ensure Keycloak database exists and is accessible

### Debug Logging

Enable debug logging by setting:
```yaml
logging:
  level:
    com.ruoyi.common.keycloak: DEBUG
    com.ruoyi.gateway.filter.AuthFilter: DEBUG
```

## Security Considerations

- Keycloak tokens are validated at the gateway level
- User information is extracted and forwarded to backend services
- Both token types (JWT and Keycloak) are supported simultaneously
- Token expiration and refresh are handled by the frontend
- User synchronization ensures local user data consistency

## Next Steps

1. Configure Keycloak realm with your specific requirements
2. Set up user roles and permissions in Keycloak
3. Configure LDAP/AD integration if needed
4. Set up production-ready Keycloak with proper database and clustering
5. Implement token refresh logic in the frontend
