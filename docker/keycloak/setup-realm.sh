#!/bin/bash

# Keycloak Realm Setup Script
# This script creates the RuoYi realm and configures clients

KEYCLOAK_URL="http://localhost:8089"
ADMIN_USER="admin"
ADMIN_PASSWORD="admin"
REALM_NAME="ruoyi"

echo "Setting up Keycloak realm and clients..."

# Wait for Keycloak to be ready
echo "Waiting for Keycloak to start..."
until curl -f ${KEYCLOAK_URL}/health/ready; do
    echo "Waiting for Keycloak..."
    sleep 5
done

echo "Keycloak is ready. Setting up realm..."

# Get admin token
ADMIN_TOKEN=$(curl -s -X POST "${KEYCLOAK_URL}/realms/master/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=${ADMIN_USER}" \
  -d "password=${ADMIN_PASSWORD}" \
  -d "grant_type=password" \
  -d "client_id=admin-cli" | jq -r '.access_token')

if [ "$ADMIN_TOKEN" = "null" ] || [ -z "$ADMIN_TOKEN" ]; then
    echo "Failed to get admin token"
    exit 1
fi

echo "Got admin token, creating realm..."

# Create realm
curl -s -X POST "${KEYCLOAK_URL}/admin/realms" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "realm": "'${REALM_NAME}'",
    "enabled": true,
    "displayName": "RuoYi Realm",
    "registrationAllowed": true,
    "loginWithEmailAllowed": true,
    "duplicateEmailsAllowed": false,
    "resetPasswordAllowed": true,
    "editUsernameAllowed": true,
    "bruteForceProtected": true
  }'

echo "Realm created. Creating frontend client..."

# Create frontend client
curl -s -X POST "${KEYCLOAK_URL}/admin/realms/${REALM_NAME}/clients" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "ruoyi-frontend",
    "name": "RuoYi Frontend Client",
    "description": "Frontend client for RuoYi application",
    "enabled": true,
    "clientAuthenticatorType": "client-secret",
    "redirectUris": ["http://localhost:80/*", "http://localhost:8080/*"],
    "webOrigins": ["http://localhost:80", "http://localhost:8080"],
    "protocol": "openid-connect",
    "publicClient": true,
    "standardFlowEnabled": true,
    "implicitFlowEnabled": false,
    "directAccessGrantsEnabled": true,
    "serviceAccountsEnabled": false,
    "fullScopeAllowed": true
  }'

echo "Frontend client created. Creating backend client..."

# Create backend client
curl -s -X POST "${KEYCLOAK_URL}/admin/realms/${REALM_NAME}/clients" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "ruoyi-backend",
    "name": "RuoYi Backend Client",
    "description": "Backend client for RuoYi application",
    "enabled": true,
    "clientAuthenticatorType": "client-secret",
    "protocol": "openid-connect",
    "publicClient": false,
    "standardFlowEnabled": true,
    "implicitFlowEnabled": false,
    "directAccessGrantsEnabled": true,
    "serviceAccountsEnabled": true,
    "fullScopeAllowed": true,
    "bearerOnly": true
  }'

echo "Backend client created. Creating test user..."

# Create test user
curl -s -X POST "${KEYCLOAK_URL}/admin/realms/${REALM_NAME}/users" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "enabled": true,
    "emailVerified": true,
    "credentials": [{
      "type": "password",
      "value": "password",
      "temporary": false
    }]
  }'

echo "Test user created."
echo ""
echo "Keycloak setup completed!"
echo "Realm: ${REALM_NAME}"
echo "Frontend Client: ruoyi-frontend"
echo "Backend Client: ruoyi-backend"
echo "Test User: testuser / password"
echo ""
echo "You can access the admin console at: ${KEYCLOAK_URL}/admin"
echo "Admin credentials: ${ADMIN_USER} / ${ADMIN_PASSWORD}"
