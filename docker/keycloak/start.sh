#!/bin/bash

# Keycloak启动脚本
# 使用开发模式启动Keycloak服务器

echo "Starting Keycloak server..."

docker run -d \
  --name ruoyi-keycloak \
  -p 8089:8080 \
  -e KC_HOSTNAME=localhost \
  -e KC_HOSTNAME_PORT=8089 \
  -e KC_HOSTNAME_STRICT=false \
  -e KC_HOSTNAME_STRICT_HTTPS=false \
  -e KC_HTTP_ENABLED=true \
  -e KC_HEALTH_ENABLED=true \
  -e KC_METRICS_ENABLED=true \
  -e KEYCLOAK_ADMIN=admin \
  -e KEYCLOAK_ADMIN_PASSWORD=admin \
  quay.io/keycloak/keycloak:22.0.5 \
  start-dev

echo "Keycloak server started successfully!"
echo "Admin console: http://localhost:8089/admin"
echo "Username: admin"
echo "Password: admin"
echo ""
echo "Please wait a few minutes for Keycloak to fully start up."
echo "You can check the logs with: docker logs ruoyi-keycloak"
