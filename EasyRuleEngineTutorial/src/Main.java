import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.core.DefaultRulesEngine;

public class Main {
    public static void main(String[] args) {
        System.out.println("Easy Rules Tutorial");
        
        // Define facts
        Facts facts = new Facts();
        facts.put("temperature", 30);
        facts.put("rainPrediction", 80);
        
        // Create rules
        WeatherRule weatherRule = new WeatherRule();
        RainRule rainRule = new RainRule();
        
        // Register rules
        Rules rules = new Rules();
        rules.register(weatherRule);
        rules.register(rainRule);
        
        // Create a rules engine and fire rules
        RulesEngine rulesEngine = new DefaultRulesEngine();
        rulesEngine.fire(rules, facts);
        
        // Demonstrate rule conditions
        System.out.println("\nChanging facts and firing rules again:");
        facts.put("temperature", 20);
        facts.put("rainPrediction", 30);
        rulesEngine.fire(rules, facts);
    }
}