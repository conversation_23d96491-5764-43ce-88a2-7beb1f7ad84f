import org.jeasy.rules.annotation.Action;
import org.jeasy.rules.annotation.Condition;
import org.jeasy.rules.annotation.Priority;
import org.jeasy.rules.annotation.Rule;
import org.jeasy.rules.api.Facts;

@Rule(name = "rain rule", description = "if rain prediction > 70%, then it will rain")
public class RainRule {
    
    @Condition
    public boolean checkRainPrediction(Facts facts) {
        return (int) facts.get("rainPrediction") > 70;
    }

//    @Action
    private void doSomething(Facts facts){
        System.out.println("do something");
    }

//    @Action
    private void reportRainForecast(Facts facts) {
        System.out.println("It will rain today! Rain prediction: " + facts.get("rainPrediction") + "%");
    }

    @Action
    public void sequenceOfWorks(Facts facts) {
        reportRainForecast(facts);
        doSomething(facts);

    }

    @Priority
    public int getPriority() {
        return 1;
    }
}