import org.jeasy.rules.annotation.Action;
import org.jeasy.rules.annotation.Condition;
import org.jeasy.rules.annotation.Priority;
import org.jeasy.rules.annotation.Rule;
import org.jeasy.rules.api.Facts;

@Rule(name = "weather rule", description = "if temperature > 25, then it's hot")
public class WeatherRule {
    
    @Condition
    public boolean checkTemperature(Facts facts) {
        return (int) facts.get("temperature") > 25;
    }
    
    @Action
    public void reportHotWeather(Facts facts) {
        System.out.println("It's hot today! Temperature: " + facts.get("temperature") + "°C");
    }
    
    @Priority
    public int getPriority() {
        return 2;
    }
}